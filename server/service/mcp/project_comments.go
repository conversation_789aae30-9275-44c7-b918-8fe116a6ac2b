package mcp

import (
	"context"
	"errors"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	systemModel "github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ProjectCommentsService struct{}

// CreateProjectComment 创建项目评论
func (s *ProjectCommentsService) CreateProjectComment(ctx context.Context, req *request.CreateProjectCommentRequest, userUUID uuid.UUID) error {
	// 检查项目是否存在
	var project mcp.Projects
	if err := global.GVA_DB.Where("id = ? AND status = ?", req.ProjectID, mcp.ProjectStatusCreated).First(&project).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("项目不存在")
		}
		return err
	}

	// 检查用户是否已经评论过该项目
	var existingComment mcp.ProjectComments
	err := global.GVA_DB.Where("project_id = ? AND user_uuid = ? AND status != ?", 
		req.ProjectID, userUUID, mcp.CommentStatusDeleted).First(&existingComment).Error
	
	if err == nil {
		return errors.New("您已经评论过该项目")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 创建评论
	comment := mcp.ProjectComments{
		ProjectID:   req.ProjectID,
		UserUUID:    userUUID,
		Rating:      req.Rating,
		Content:     req.Content,
		IsAnonymous: req.IsAnonymous,
		Status:      mcp.CommentStatusActive,
	}

	// 开启事务
	tx := global.GVA_DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 创建评论记录
	if err := tx.Create(&comment).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 更新项目的平均评分和评论数
	if err := s.updateProjectRatingStats(tx, req.ProjectID); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// UpdateProjectComment 更新项目评论
func (s *ProjectCommentsService) UpdateProjectComment(ctx context.Context, req *request.UpdateProjectCommentRequest, userUUID uuid.UUID) error {
	// 查找评论
	var comment mcp.ProjectComments
	if err := global.GVA_DB.Where("id = ? AND user_uuid = ? AND status != ?", 
		req.ID, userUUID, mcp.CommentStatusDeleted).First(&comment).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("评论不存在或无权限修改")
		}
		return err
	}

	// 开启事务
	tx := global.GVA_DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 更新评论
	updateData := map[string]interface{}{
		"rating":       req.Rating,
		"content":      req.Content,
		"is_anonymous": req.IsAnonymous,
	}
	
	if err := tx.Model(&comment).Updates(updateData).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 更新项目的平均评分和评论数
	if err := s.updateProjectRatingStats(tx, comment.ProjectID); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// DeleteProjectComment 删除项目评论
func (s *ProjectCommentsService) DeleteProjectComment(ctx context.Context, commentID uint, userUUID uuid.UUID) error {
	// 查找评论
	var comment mcp.ProjectComments
	if err := global.GVA_DB.Where("id = ? AND user_uuid = ? AND status != ?", 
		commentID, userUUID, mcp.CommentStatusDeleted).First(&comment).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("评论不存在或无权限删除")
		}
		return err
	}

	// 开启事务
	tx := global.GVA_DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 软删除评论
	if err := tx.Model(&comment).Update("status", mcp.CommentStatusDeleted).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 更新项目的平均评分和评论数
	if err := s.updateProjectRatingStats(tx, comment.ProjectID); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// GetProjectCommentsList 获取项目评论列表
func (s *ProjectCommentsService) GetProjectCommentsList(ctx context.Context, req request.ProjectCommentsListRequest) (list interface{}, total int64, err error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	
	// 设置默认状态
	if req.Status == "" {
		req.Status = mcp.CommentStatusActive
	}

	db := global.GVA_DB.Model(&mcp.ProjectComments{})
	
	// 基础查询条件
	db = db.Where("project_id = ? AND status = ?", req.ProjectID, req.Status)
	
	// 评分过滤
	if req.Rating != nil && *req.Rating > 0 {
		db = db.Where("rating = ?", *req.Rating)
	}

	var comments []mcp.ProjectComments
	
	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 获取列表数据，按创建时间倒序
	err = db.Order("created_at DESC").Limit(limit).Offset(offset).Find(&comments).Error
	if err != nil {
		return
	}

	// 填充用户信息
	for i := range comments {
		if !comments[i].IsAnonymous {
			var user systemModel.SysUser
			if err := global.GVA_DB.Where("uuid = ?", comments[i].UserUUID).First(&user).Error; err == nil {
				comments[i].UserName = user.Username
				comments[i].UserAvatar = user.HeaderImg
			}
		} else {
			comments[i].UserName = "匿名用户"
			comments[i].UserAvatar = ""
		}
	}

	return comments, total, nil
}

// GetProjectCommentByID 根据ID获取评论详情
func (s *ProjectCommentsService) GetProjectCommentByID(ctx context.Context, commentID uint) (*mcp.ProjectComments, error) {
	var comment mcp.ProjectComments
	if err := global.GVA_DB.Where("id = ? AND status != ?", commentID, mcp.CommentStatusDeleted).First(&comment).Error; err != nil {
		return nil, err
	}

	// 填充用户信息
	if !comment.IsAnonymous {
		var user systemModel.SysUser
		if err := global.GVA_DB.Where("uuid = ?", comment.UserUUID).First(&user).Error; err == nil {
			comment.UserName = user.Username
			comment.UserAvatar = user.HeaderImg
		}
	} else {
		comment.UserName = "匿名用户"
		comment.UserAvatar = ""
	}

	return &comment, nil
}

// GetProjectRatingStats 获取项目评分统计
func (s *ProjectCommentsService) GetProjectRatingStats(ctx context.Context, projectID uint) (map[string]interface{}, error) {
	var stats struct {
		TotalComments int64   `json:"totalComments"`
		AverageRating float64 `json:"averageRating"`
		Rating1Count  int64   `json:"rating1Count"`
		Rating2Count  int64   `json:"rating2Count"`
		Rating3Count  int64   `json:"rating3Count"`
		Rating4Count  int64   `json:"rating4Count"`
		Rating5Count  int64   `json:"rating5Count"`
	}

	db := global.GVA_DB.Model(&mcp.ProjectComments{}).Where("project_id = ? AND status = ?", projectID, mcp.CommentStatusActive)

	// 获取总评论数
	if err := db.Count(&stats.TotalComments).Error; err != nil {
		return nil, err
	}

	if stats.TotalComments == 0 {
		return map[string]interface{}{
			"totalComments": 0,
			"averageRating": 0.0,
			"rating1Count":  0,
			"rating2Count":  0,
			"rating3Count":  0,
			"rating4Count":  0,
			"rating5Count":  0,
		}, nil
	}

	// 获取平均评分
	var avgResult struct {
		Avg float64
	}
	if err := db.Select("AVG(rating) as avg").Scan(&avgResult).Error; err != nil {
		return nil, err
	}
	stats.AverageRating = avgResult.Avg

	// 获取各星级评论数
	for rating := 1; rating <= 5; rating++ {
		var count int64
		if err := db.Where("rating = ?", rating).Count(&count).Error; err != nil {
			return nil, err
		}
		switch rating {
		case 1:
			stats.Rating1Count = count
		case 2:
			stats.Rating2Count = count
		case 3:
			stats.Rating3Count = count
		case 4:
			stats.Rating4Count = count
		case 5:
			stats.Rating5Count = count
		}
	}

	return map[string]interface{}{
		"totalComments": stats.TotalComments,
		"averageRating": stats.AverageRating,
		"rating1Count":  stats.Rating1Count,
		"rating2Count":  stats.Rating2Count,
		"rating3Count":  stats.Rating3Count,
		"rating4Count":  stats.Rating4Count,
		"rating5Count":  stats.Rating5Count,
	}, nil
}

// updateProjectRatingStats 更新项目的评分统计信息（内部方法）
func (s *ProjectCommentsService) updateProjectRatingStats(tx *gorm.DB, projectID uint) error {
	// 计算平均评分和评论总数
	var stats struct {
		Count int64
		Avg   float64
	}

	err := tx.Model(&mcp.ProjectComments{}).
		Where("project_id = ? AND status = ?", projectID, mcp.CommentStatusActive).
		Select("COUNT(*) as count, AVG(rating) as avg").
		Scan(&stats).Error
	
	if err != nil {
		global.GVA_LOG.Error("计算项目评分统计失败", zap.Error(err), zap.Uint("projectID", projectID))
		return err
	}

	// 更新项目表中的统计信息
	updateData := map[string]interface{}{
		"comments":       stats.Count,
		"average_rating": stats.Avg,
	}

	if err := tx.Model(&mcp.Projects{}).Where("id = ?", projectID).Updates(updateData).Error; err != nil {
		global.GVA_LOG.Error("更新项目统计信息失败", zap.Error(err), zap.Uint("projectID", projectID))
		return err
	}

	global.GVA_LOG.Info("更新项目评分统计成功", 
		zap.Uint("projectID", projectID), 
		zap.Int64("commentCount", stats.Count), 
		zap.Float64("averageRating", stats.Avg))

	return nil
}

// CheckUserCommentPermission 检查用户是否有评论权限
func (s *ProjectCommentsService) CheckUserCommentPermission(ctx context.Context, projectID uint, userUUID uuid.UUID) error {
	// 检查项目是否存在且可评论
	var project mcp.Projects
	if err := global.GVA_DB.Where("id = ? AND status = ?", projectID, mcp.ProjectStatusCreated).First(&project).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("项目不存在或不可评论")
		}
		return err
	}

	// 检查用户是否已经评论过
	var count int64
	if err := global.GVA_DB.Model(&mcp.ProjectComments{}).
		Where("project_id = ? AND user_uuid = ? AND status != ?", 
			projectID, userUUID, mcp.CommentStatusDeleted).
		Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		return errors.New("您已经评论过该项目")
	}

	return nil
}
