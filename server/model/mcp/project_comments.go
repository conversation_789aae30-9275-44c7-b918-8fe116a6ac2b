package mcp

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/google/uuid"
)

// ProjectComments 项目评论表
type ProjectComments struct {
	global.GVA_MODEL
	ProjectID   uint      `json:"projectId" form:"projectId" gorm:"column:project_id;comment:项目ID;not null;index"`        // 项目ID
	UserUUID    uuid.UUID `json:"userUuid" form:"userUuid" gorm:"column:user_uuid;comment:用户UUID;not null;index"`        // 用户UUID
	Rating      int       `json:"rating" form:"rating" gorm:"column:rating;comment:评分(1-5星);not null"`                    // 评分(1-5星)
	Content     string    `json:"content" form:"content" gorm:"column:content;comment:评论内容;type:text;not null"`           // 评论内容
	IsAnonymous bool      `json:"isAnonymous" form:"isAnonymous" gorm:"column:is_anonymous;comment:是否匿名;default:false"` // 是否匿名评论
	Status      string    `json:"status" form:"status" gorm:"column:status;comment:状态;type:varchar(20);default:'pending'"` // 状态：pending-待审核，approved-审核通过，rejected-审核拒绝，hidden-隐藏，deleted-删除

	// 审核相关字段
	ReviewerUUID *uuid.UUID `json:"reviewerUuid" form:"reviewerUuid" gorm:"column:reviewer_uuid;comment:审核人UUID;index"`     // 审核人UUID
	ReviewedAt   *time.Time `json:"reviewedAt" form:"reviewedAt" gorm:"column:reviewed_at;comment:审核时间"`                    // 审核时间
	ReviewNote   string     `json:"reviewNote" form:"reviewNote" gorm:"column:review_note;comment:审核备注;type:text"`          // 审核备注

	// 关联字段（不存储在数据库中）
	UserName     string `json:"userName" gorm:"-"`     // 用户名
	UserAvatar   string `json:"userAvatar" gorm:"-"`   // 用户头像
	ProjectName  string `json:"projectName" gorm:"-"`  // 项目名称
	ReviewerName string `json:"reviewerName" gorm:"-"` // 审核人姓名
}

// TableName 设置表名
func (ProjectComments) TableName() string {
	return "project_comments"
}

// CommentStatus 评论状态常量
const (
	CommentStatusPending  = "pending"  // 待审核
	CommentStatusApproved = "approved" // 审核通过
	CommentStatusRejected = "rejected" // 审核拒绝
	CommentStatusActive   = "active"   // 正常显示（兼容旧数据）
	CommentStatusHidden   = "hidden"   // 隐藏
	CommentStatusDeleted  = "deleted"  // 删除
)
